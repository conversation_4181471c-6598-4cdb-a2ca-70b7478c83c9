"""
LLM Response Generator Module for Rwenzori Innovations Game

This module provides functions to generate responses using the Gemini API
with OpenAI-compatible format.
"""

import logging
import os
import random

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Try to import OpenAI, but don't fail if it's not available
try:
    from openai import OpenAI
    from dotenv import load_dotenv
    # Load environment variables
    load_dotenv()
    OPENAI_AVAILABLE = True
    logging.info("OpenAI package available. Using OpenAI API for response generation.")
except ImportError:
    OPENAI_AVAILABLE = False
    logging.warning("OpenAI package not available. Using mock responses for testing.")

def get_guidelines_for_task(task_id):
    """
    Get the guidelines for a specific task.

    Args:
        task_id: The ID of the task

    Returns:
        A list of guidelines for the task
    """
    # This is a simplified version - in a real implementation,
    # these would be loaded from a database or configuration file
    guidelines = {
        "cover_letter": [
            "Address the specific requirements mentioned in the job posting",
            "Highlight relevant skills and experiences",
            "Maintain a professional tone",
            "Keep it concise (1 page maximum)",
            "Include a proper greeting and closing",
            "Express enthusiasm for the position"
        ],
        "job_analysis": [
            "Identify all key skills and qualifications",
            "Categorize responsibilities clearly",
            "Analyze the work environment and expectations",
            "Present information in a structured format",
            "Include both technical and soft skill requirements",
            "Highlight any unique aspects of the position"
        ],
        "customer_service": [
            "Begin with a sincere apology for the inconvenience",
            "Address all specific issues mentioned by the customer",
            "Provide clear solutions and next steps",
            "Maintain a professional and empathetic tone",
            "Include specific compensation or resolution details",
            "Close with a commitment to improve"
        ],
        "onboarding_checklist": [
            "Create a comprehensive day-by-day schedule",
            "Include administrative, training, and social elements",
            "Organize items in a logical sequence",
            "Use clear, actionable checklist items",
            "Cover the entire first week of employment",
            "Include follow-up actions for week two"
        ],
        "market_analysis": [
            "Analyze each market with specific data points",
            "Provide clear recommendations with justification",
            "Include risk assessment and mitigation strategies",
            "Structure the analysis in a professional format",
            "Consider both short and long-term implications",
            "Address competitive landscape considerations"
        ],
        "sales_pitch": [
            "Start with an attention-grabbing opening",
            "Clearly articulate the value proposition",
            "Address specific pain points of the target audience",
            "Include concrete benefits and ROI information",
            "Use persuasive but professional language",
            "End with a clear call to action"
        ],
        "competitor_analysis": [
            "Provide detailed comparison of features and pricing",
            "Identify competitive advantages and vulnerabilities",
            "Include specific positioning recommendations",
            "Use data and metrics to support analysis",
            "Consider market trends and future developments",
            "Organize information in a clear, structured format"
        ]
    }

    # Return guidelines for the task or a default set if not found
    return guidelines.get(task_id, ["Be professional", "Be thorough", "Be clear and concise"])

def format_guidelines_for_prompt(guidelines):
    """
    Format a list of guidelines into a string for inclusion in a prompt.

    Args:
        guidelines: A list of guideline strings

    Returns:
        A formatted string of guidelines
    """
    return "\n".join([f"- {guideline}" for guideline in guidelines])

def generate_response_with_llm(prompt, task_id=None, purpose="task_response", max_retries=2):
    """
    Generate a response to the user's prompt using the Gemini API.

    Args:
        prompt: The user's prompt
        task_id: The ID of the current task (optional)
        purpose: The purpose of the generation (task_response or preview)
        max_retries: Maximum number of retry attempts

    Returns:
        The generated response text
    """
    # Get guidelines for the task
    guidelines = get_guidelines_for_task(task_id)

    # Format guidelines for the prompt
    formatted_guidelines = format_guidelines_for_prompt(guidelines)

    # Construct the generation prompt based on purpose
    if purpose == "preview":
        generation_prompt = f"""
You are an AI assistant at Rwenzori Innovations. You need to generate a complete response to a user's prompt.

User's Prompt:
{prompt}

Generate a detailed, professional response that fully addresses the user's request.
If this is a cover letter task, create a complete cover letter with all necessary sections.
"""
    else:  # task_response or any other purpose
        generation_prompt = f"""
You are an AI assistant at Rwenzori Innovations. You need to respond to a user's prompt.
The response should follow these guidelines:

{formatted_guidelines}

User's Prompt:
{prompt}

Generate a detailed, helpful response that follows all the guidelines above.
Make sure your response is professional, well-structured, and addresses all the key points.
"""

    # Use the Gemini API key from environment or use the default one
    api_key = os.environ.get("GEMINI_API_KEY", "AIzaSyDTjwdVbjGu5Q0v-dP6QsfC4IoKM36g8l8")

    # Add offline mode check
    if os.environ.get("LLM_SERVICE_OFFLINE", "false").lower() == "true":
        logging.warning("LLM service is marked as offline. Using mock responses.")
        raise Exception("LLM service is offline")

    # If OpenAI is not available, return a mock response for testing
    if not OPENAI_AVAILABLE:
        logging.info(f"Using mock response for testing (OpenAI not available)")

        # Generate a mock response based on the purpose
        if purpose == "preview":
            if task_id == "cover_letter":
                return f"""Dear Hiring Manager,

I am writing to express my enthusiastic interest in the Junior Assistant position at Rwenzori Innovations Ltd. as advertised. Having followed Rwenzori Innovations Ltd.'s innovative contributions to the solar solutions industry for some time, I am impressed by your commitment to quality and exceptional customer service, and I am eager to contribute to your team's success.

My background and skills align well with the requirements of this role. I possess excellent communication skills, both written and verbal, honed through academic experiences and previous roles. My strong attention to detail and organizational abilities have proven valuable in managing multiple tasks and prioritizing effectively. My proficiency in the Microsoft Office Suite, including Word, Excel, and PowerPoint, enables me to efficiently prepare documents, presentations, and manage data.

In my previous role as [Previous Role if applicable, otherwise remove this line], I was responsible for [mention relevant responsibilities and accomplishments that align with the job description. E.g., "coordinating meetings, managing calendars, and maintaining organized filing systems"]. I consistently demonstrated a proactive attitude, identifying ways to support various departments and contribute to team projects and initiatives. I am confident in my ability to quickly adapt to new environments and contribute meaningfully to your operations.

While my Bachelor's degree in [mention your degree if you have one, otherwise remove this line] has equipped me with valuable knowledge, I am eager to learn and develop my professional skills within a dynamic organization like Rwenzori Innovations Ltd. The opportunity to support various departments and contribute to the smooth operation of your office is particularly appealing.

Thank you for considering my application. I am excited about the possibility of joining your team and contributing to Rwenzori Innovations Ltd.'s continued success. I have attached my resume for your review and welcome the opportunity to discuss my qualifications further in an interview.

Sincerely,
[Your Name]
[Your Contact Information]"""
            else:
                return f"""Dear Hiring Manager,

I am writing to express my interest in the position at your company based on the prompt: "{prompt[:50]}...".

This response addresses all the key requirements mentioned in your job posting and highlights my relevant qualifications and experience. I've structured this as a complete, professional cover letter that would be ready to submit with a job application.

Thank you for considering my application. I look forward to the opportunity to discuss how my skills and experiences align with your needs.

Sincerely,
[Candidate Name]"""
        else:
            # Mock responses for different task types
            mock_responses = {
                "cover_letter": f"""Dear Hiring Manager,

I am writing to express my interest in the Software Engineer position at your company. Based on your prompt "{prompt[:30]}...", I have crafted this cover letter to highlight my relevant skills and experiences.

Throughout my career, I have developed strong technical skills in software development, problem-solving, and teamwork. I am particularly experienced in [specific technologies mentioned in the prompt].

I am excited about the opportunity to join your team and contribute to your company's success. My background in [relevant experience] makes me a strong candidate for this position.

Thank you for considering my application. I look forward to the opportunity to discuss how my skills and experiences align with your needs.

Sincerely,
[Candidate Name]""",

                "meeting_summary": f"""# Team Meeting Summary

## Key Discussion Points
- Discussed project timeline and milestones
- Reviewed current progress on feature development
- Addressed challenges with the integration testing

## Decisions Made
- Agreed to extend the deadline for Phase 1 by one week
- Approved the new design for the user dashboard
- Decided to prioritize bug fixes over new feature development

## Action Items
- John: Complete the API documentation by Friday
- Sarah: Schedule a follow-up meeting with the client
- Team: Review and provide feedback on the new design by Wednesday

This summary addresses the key points from your prompt: "{prompt[:30]}...".""",

                "default": f"""Thank you for your prompt: "{prompt[:50]}..."

I've analyzed your request and prepared a comprehensive response that addresses all the key points you mentioned. Following the guidelines for this task, I've structured my response to be professional, thorough, and actionable.

The main points I've addressed include:
1. [First key point from the prompt]
2. [Second key point from the prompt]
3. [Third key point from the prompt]

I've also included specific recommendations and next steps based on your requirements. This response is designed to be both informative and practical, giving you clear guidance on how to proceed.

Please let me know if you need any clarification or have additional questions about any part of this response."""
            }

            # Return the appropriate mock response or the default one
            return mock_responses.get(task_id, mock_responses["default"])

    # If OpenAI is available, use the API
    retry_count = 0
    while retry_count <= max_retries:
        try:
            logging.info(f"Calling Gemini API for response generation for task {task_id}")
            logging.info(f"Guidelines: {formatted_guidelines[:100]}...")
            logging.info(f"User prompt: {prompt}")

            # Initialize the OpenAI client with Gemini configuration
            try:
                client = OpenAI(
                    api_key=api_key,
                    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
                )
            except TypeError as te:
                # Handle the case where 'proxies' parameter is not supported
                if "proxies" in str(te):
                    logging.warning("OpenAI client doesn't support 'proxies' parameter, trying without it")
                    client = OpenAI(
                        api_key=api_key,
                        base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
                    )
                else:
                    raise te

            # Prepare messages for the API call
            messages = [
                {"role": "system", "content": "You are an AI assistant at Widget Makers, Inc. You provide professional, helpful responses."},
                {"role": "user", "content": generation_prompt}
            ]

            logging.info(f"Sending request to Gemini API with system prompt and user prompt")

            # Make the API call
            response = client.chat.completions.create(
                model="gemini-2.5-flash-lite-preview-06-17",  # Using Gemini model
                messages=messages,
                temperature=0.7,  # Higher temperature for more creative responses
                max_tokens=1500
            )

            # Extract the response content
            content = response.choices[0].message.content

            logging.info(f"Successfully received response from Gemini API")
            logging.info(f"Generated response (first 100 chars): {content[:100]}...")

            return content

        except Exception as e:
            error_msg = str(e)
            logging.error(f"Error calling Gemini API for response generation: {error_msg}")

            # Check for specific error conditions
            if "offline" in error_msg.lower():
                logging.warning("LLM service appears to be offline")
                os.environ["LLM_SERVICE_OFFLINE"] = "true"
                raise Exception("LLM service appears to be offline")

            retry_count += 1
            if retry_count <= max_retries:
                logging.info(f"Retrying API call (attempt {retry_count}/{max_retries})")
                import time
                time.sleep(1)  # Add a small delay between retries
            else:
                logging.error(f"Failed to generate response after {max_retries} attempts")
                # Return a fallback response
                return f"""I apologize, but I'm currently unable to generate a detailed response due to technical difficulties.

Here's a general response based on your prompt:

Your prompt about "{prompt[:50]}..." has been received. In a normal situation, I would provide a comprehensive response following all the guidelines for this task.

Please try again later when the service is available."""
